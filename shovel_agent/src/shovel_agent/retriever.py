from langchain_chroma import Chroma
from chromadb import HttpClient
import os
from dotenv import load_dotenv

from shovel_agent.llms import get_embeddings

load_dotenv()
embeddings = get_embeddings()
chroma_store = Chroma(persist_directory=os.getenv('CHROMA_PERSIST_DIR'), embedding_function=embeddings, client=HttpClient(host=os.getenv('CHROMA_HOST')))

from langchain.tools.retriever import create_retriever_tool

retriever_tool = create_retriever_tool(
    chroma_store.as_retriever(search_type=os.getenv('SEARCH_TYPE', 'similarity'),search_kwargs={'k': int(os.getenv('SEARCH_TOP_K', 1)),'score_threshold': float(os.getenv('SEARCH_SCORE_THRESHOLD', 0.2))}),
    "retrieve_defi_knowledge",
    # "专业的DeFi中文知识库, query参数请翻译为中文",
    "Search and return Defi Knowledge.",
    # response_format="content_and_artifact"
)

# @tool(parse_docstring=True)
# def retriever_tool(query: str) -> str:
#     """
#     专业的DeFi中文知识库
#
#     Args:
#         query (str): 用户要查询的问题。要求为中文
#
#     Returns:
#         str: 用户问题相关的文档。
#
#     """
#     retriever = create_retriever_tool(
#         chroma_store.as_retriever(search_type='similarity_score_threshold',search_kwargs={'k': 3,'score_threshold':0.5}),
#         "retrieve_defi_knowledge",
#         "专业的DeFi中文知识库, query参数请翻译为中文",
#         # "Search and return defi Knowledge, translate query to chinese before searching.",
#         # response_format="content_and_artifact"
#     )
#     return retriever.invoke(query)
import json
from typing import Optional

from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.constants import CONF, CONFIG_KEY_THREAD_ID, START
from langgraph.graph.state import CompiledStateGraph

from shovel_agent.chat_history import ChatHistory
from shovel_agent.constants import MESSAGES, ChANNEL_VALUES, CONTENT, USER_ID, TS
from shovel_agent.graph import builder
from shovel_agent.memory import get_checkpointer
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import uvicorn
from dotenv import load_dotenv
from pydantic import BaseModel
import os
from collections.abc import Sequence
from langgraph.types import StreamMode
from fastapi.middleware.cors import CORSMiddleware
from langchain_core.messages import BaseMessage, message_to_dict

checkpointer: BaseCheckpointSaver
graph: CompiledStateGraph
chat_history = ChatHistory()
app = FastAPI()
class GenRequest(BaseModel):
    """GenRequest 请求模型

    Attributes:
        content (str): 用户输入内容，默认为空字符串。
        user_id (str): 用户 ID，默认为 "guest"。
        thread_id (str): 线程 ID，默认为 "thread-0"。
        stream (bool): 是否开启流式输出，默认为 False。
        stream_mode (StreamMode | Sequence[StreamMode] | None): 流式输出模式，默认为 None。流式输出模式的取值范围如下：
            - "values"：在每一步之后发出状态中的所有值，包括中断。当与函数式 API 一起使用时，值会在工作流结束时发出一次。
            - "updates"：在每一步之后仅发出节点或任务名称以及节点或任务返回的更新。如果在同一步骤中进行了多次更新（例如运行了多个节点），则这些更新将单独发出。
            - "custom"：使用 `StreamWriter` 从节点或任务内部发出自定义数据。
            - "messages"：对于节点或任务内部的任何大语言模型调用，逐个 token 发出大语言模型消息以及元数据。将以二元组 `(大语言模型 token, 元数据)` 的形式发出。
            - "debug"：在每一步发出尽可能多信息的调试事件。
            你可以传递一个列表作为 `StreamMode` 参数，以同时流式输出多种模式。流式输出将是 `(模式, 数据)` 的元组。
            更多详细信息请参阅 `https://langchain-ai.github.io/langgraph/how-tos/streaming/`。
    """
    content: str = ""
    user_id: str = "guest"
    thread_id: str = "thread-0"
    stream: bool = False
    stream_mode: StreamMode | Sequence[StreamMode] | None = None

@app.api_route("/alphafi/agent/gen", methods=["POST"], summary="生成内容接口", description="根据用户输入生成内容，支持流式和非流式输出。当前仅支持messages模式返回json; 若消息类型为tool并且name带有_action后缀，则表示链上交互，content中的一项为raw_transaction,代表未签名的交易")
async def gen(request: GenRequest):
    """生成内容接口

    Args:
        request (GenRequest): 请求参数模型。

    Returns:
        Union[StreamingResponse, str]: 流式输出时返回 StreamingResponse，非流式输出时返回json字符串。
        若消息类型为tool并且name带有_action后缀，则表示链上交互，content中的一项为raw_transaction,代表未签名的交易.{"type":"tool","data":{"name": "stake_action", "content": "{\"raw_transaction\": \"to be signed\", \"amount\": 1.0}"}}
    """

    if 'redis' == os.getenv('CHECKPOINT_DB_TYPE', ''):
        chat_history.store_message(request.user_id, request.thread_id, request.content)
    if request.stream:
        async def generate():
            async for chuck in graph.astream(
                {"messages": [{"role":"user", "content": request.content}]},
                {CONF:{USER_ID: request.user_id, CONFIG_KEY_THREAD_ID: request.thread_id}},
                stream_mode=request.stream_mode,
            ):
                # TODO: 当前仅支持messages模式返回json
                yield json.dumps(message_to_dict(chuck[0])) if request.stream_mode == "messages" else chuck
        return StreamingResponse(generate())
    else:
        result = await graph.ainvoke(
            {"messages": [{"role":"user", "content": request.content}]},
            {CONF:{USER_ID: request.user_id, CONFIG_KEY_THREAD_ID: request.thread_id}},
        )
        response = {
            "messages": [
                message_to_dict(msg)
                if isinstance(msg, BaseMessage) else msg
                for msg in result["messages"]
            ]
        }
        return response
        # 返回值为字符串 return json.dumps(response)

@app.get("/alphafi/agent/thread/list/{user_id}", summary="获取用户线程列表接口", description="根据用户 ID 获取包含 messages,thread_id和ts 的线程列表")
async def get_thread_list(user_id: str):
    """获取用户线程列表接口

    Args:
        user_id (str): 用户 ID

    Returns:
        list: 包含 messages, thread_id和 ts 的字典类型列表,且按照ts倒序排列
    """
    thread_list = []
    if 'redis' == os.getenv('CHECKPOINT_DB_TYPE', ''):
        thread_list = chat_history.get_user_messages(user_id)
    else:
        # redis不支持仅支持step 和 source作为filter
        # states = list(checkpointer.list(None, filter={USER_ID: user_id}))
        states = list(await checkpointer.alist(None))
        # 如果states不为空，则遍历states
        if states:
            # 初始化thread_list变量
            thread_list = []
            thread_id = states[0].config[CONF][CONFIG_KEY_THREAD_ID]
            ts = states[0].checkpoint[TS]
            thread_start_msg = ''
            for state in states:
                if thread_id != state.config[CONF][CONFIG_KEY_THREAD_ID]:
                    thread_list.append({MESSAGES: thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS: ts})
                    thread_start_msg = ''
                    thread_id = state.config[CONF][CONFIG_KEY_THREAD_ID]
                elif START in state.checkpoint[ChANNEL_VALUES]:
                    # 当前只获取用户的输入的检查点
                    thread_start_msg = state.checkpoint[ChANNEL_VALUES][START][MESSAGES][0][CONTENT]
                    ts = state.checkpoint[TS]
                    pass
                    # result = {MESSAGES:state.checkpoint[CHANNEL_VALUES][START][MESSAGES][0][CONTENT],CONFIG_KEY_CHECKPOINT_ID: state.checkpoint[ID],CONFIG_KEY_THREAD_ID:state.config[CONF][CONFIG_KEY_THREAD_ID]}
                    # print(result)
            thread_list.append({MESSAGES: thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS: ts})
    return sorted(thread_list, key=lambda x: x.get(TS, ''), reverse=True)

@app.get("/alphafi/agent/thread/msg/{thread_id}", summary="获取线程最新状态接口", description="根据线程 ID 和用户 ID 获取最最新状态")
async def get_thread_messages(thread_id: str, user_id: str):
    """获取线程最新状态接口

    Args:
        thread_id (str): 线程 ID
        user_id (str): 用户 ID

    Returns:
        list: 最新消息列表
    """
    state = await graph.aget_state({CONF:{CONFIG_KEY_THREAD_ID: thread_id}})
    if state and MESSAGES in state.values:
        return {
            MESSAGES: [
                message_to_dict(msg)
                if isinstance(msg, BaseMessage) else msg
                for msg in state.values[MESSAGES]
            ]
        }
    else:
        return {
            MESSAGES: []
        }


async def __global_init__():
    load_dotenv()
    global checkpointer, graph
    checkpointer = await get_checkpointer()
    graph = builder.compile(checkpointer=checkpointer, name="AlphaFiAgent")


if __name__ == "__main__":
    import asyncio
    asyncio.run(__global_init__())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
    # @app.middleware("http")
    # async def options_middleware(request, call_next):
    #     if request.method == "OPTIONS":
    #         return Response(headers={
    #             "Access-Control-Allow-Origin": "*",
    #             # "Access-Control-Allow-Methods": "*",
    #             # "Access-Control-Allow-Headers": "*",
    #             # "Access-Control-Allow-Credentials": "true"
    #         })
    #     return await call_next(request)
    port = int(os.getenv('PORT', 8000))
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )
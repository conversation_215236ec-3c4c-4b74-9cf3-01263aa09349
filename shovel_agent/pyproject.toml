[project]
name = "shovel-agent"
version = "0.0.1"
description = "An agent with a tool to save long-term memory."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = { text = "MIT" }
# set < 4.0 for langchain-text-splitters;  <3.14 for langgraph-checkpoint-redis
requires-python = ">=3.10,<3.14"
dependencies = [
    "langgraph>=0.2.34",
    # Optional (for selecting different models)
    # "langchain-text-splitters>=0.3.8,<1.0.0",
    "langchain>=0.3.8",
    "langchain-core>=0.3.8",
    "python-dotenv>=1.0.1",
    "langgraph-sdk>=0.1.32",
    "langchain_ollama>=0.3.3",
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
    "langchain-chroma >=0.2.4",
    "langgraph-checkpoint-redis (>=0.0.8,<0.0.9)",
    "langchain-mcp-adapters (>=0.1.9,<0.2.0)",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1", "pytest-asyncio"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["shovel_agent"]
[tool.setuptools.package-dir]
"shovel_agent" = "src/shovel_agent"
"langgraph.templates.shovel_agent" = "src/shovel_agent"


[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = ["UP006", "UP007", "UP035", "D417", "E501"]
include = ["*.py", "*.pyi", "*.ipynb"]
[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]
"ntbk/*" = ["D", "UP", "T201"]
[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.mypy]
ignore_errors = true

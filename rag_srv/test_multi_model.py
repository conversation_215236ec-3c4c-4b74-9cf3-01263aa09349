#!/usr/bin/env python
"""
多模型RAG系统测试脚本
用于测试embedding模型和LLM模型的独立配置和切换功能
"""

import requests
import json
import time

# 服务基础URL
BASE_URL = "http://localhost:8080"

def test_get_current_config():
    """测试获取当前模型配置"""
    print("=== 测试获取当前模型配置 ===")
    response = requests.get(f"{BASE_URL}/config/models")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    return response.json()

def test_set_embedding_config(provider, model_name, base_url=None, api_key=None):
    """测试设置embedding模型配置"""
    print(f"=== 测试设置embedding模型配置: {provider} - {model_name} ===")
    
    config_data = {
        "provider": provider,
        "model_name": model_name
    }
    if base_url:
        config_data["base_url"] = base_url
    if api_key:
        config_data["api_key"] = api_key
    
    response = requests.post(f"{BASE_URL}/config/embedding", json=config_data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    return response.json()

def test_set_llm_config(provider, model_name, base_url=None, api_key=None):
    """测试设置LLM模型配置"""
    print(f"=== 测试设置LLM模型配置: {provider} - {model_name} ===")
    
    config_data = {
        "provider": provider,
        "model_name": model_name
    }
    if base_url:
        config_data["base_url"] = base_url
    if api_key:
        config_data["api_key"] = api_key
    
    response = requests.post(f"{BASE_URL}/config/llm", json=config_data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    return response.json()

def test_add_document(file_path, title=None):
    """测试添加文档"""
    print(f"=== 测试添加文档: {file_path} ===")
    
    doc_data = {"file_path": file_path}
    if title:
        doc_data["title"] = title
    
    response = requests.post(f"{BASE_URL}/documents", json=doc_data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    return response.json()

def test_query(question):
    """测试查询"""
    print(f"=== 测试查询: {question} ===")
    
    query_data = {"question": question}
    response = requests.post(f"{BASE_URL}/query", json=query_data)
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
    return result

def test_list_collections():
    """测试列出collections"""
    print("=== 测试列出collections ===")
    response = requests.get(f"{BASE_URL}/config/collections")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    return response.json()

def main():
    """主测试流程"""
    print("开始多模型RAG系统测试")
    print("=" * 50)
    
    # 1. 获取初始配置
    test_get_current_config()
    print()
    
    # 2. 测试设置ollama embedding模型
    test_set_embedding_config(
        provider="ollama",
        model_name="llama3.2:3b",
        base_url="http://10.1.177.121:11434/"
    )
    print()
    
    # 3. 测试设置gemini LLM模型
    test_set_llm_config(
        provider="gemini",
        model_name="gemini-2.0-flash",
        api_key="AIzaSyA7-ue1fLWSi1W59QFiSqeU5GXQdFp964c"
    )
    print()
    
    # 4. 查看更新后的配置
    test_get_current_config()
    print()
    
    # 5. 添加一个测试文档（如果存在的话）
    test_file = "/Users/<USER>/git/shovel/rag_srv/datas/Compound Whitepaper.pdf"
    test_add_document(test_file, "Compound协议白皮书")
    print()
    
    # 6. 测试查询
    test_query("什么是Compound协议？")
    print()
    
    # 7. 切换到gemini embedding模型
    test_set_embedding_config(
        provider="gemini",
        model_name="models/gemini-embedding-exp-03-07",
        api_key="AIzaSyA7-ue1fLWSi1W59QFiSqeU5GXQdFp964c"
    )
    print()
    
    # 8. 再次添加文档（会使用新的embedding模型）
    test_add_document(test_file, "Compound协议白皮书-Gemini")
    print()
    
    # 9. 使用新的embedding模型查询
    test_query("Compound协议的主要特点是什么？")
    print()
    
    # 10. 列出所有collections
    test_list_collections()
    print()
    
    # 11. 切换回ollama LLM模型
    test_set_llm_config(
        provider="ollama",
        model_name="llama3.2:3b",
        base_url="http://10.1.177.121:11434/"
    )
    print()
    
    # 12. 使用ollama LLM查询gemini embedding的数据
    test_query("Compound协议如何工作？")
    print()
    
    print("测试完成！")

if __name__ == "__main__":
    main()

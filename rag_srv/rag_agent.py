#!/usr/bin/env python
from langchain.agents import AgentType
from langchain.chains import create_retrieval_chain
from langchain_ollama import OllamaEmbeddings
from langchain_ollama.llms import OllamaLLM
from langchain_ollama import ChatOllama
from langchain_google_genai import ChatG<PERSON>gleGenerativeAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_chroma import Chroma
from langchain_community.document_loaders import PyPDFLoader, TextLoader
from langchain import hub
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.callbacks import StdOutCallbackHandler
from langchain.callbacks.base import BaseCallbackHandler

from chromadb import HttpClient

import os
import json
from typing import Dict, List, Any
import tiktoken

from config import (
    OLLAMA_BASE_URL, OLLAMA_MODEL, CHROMA_PERSIST_DIR, CHROMA_HOST,
    SPLITTER_CHUNK_SIZE, SPLITTER_CHUNK_OVERLAP,
    GOOGLE_API_KEY, GOOGLE_MODEL, LLM_PROVIDER,
    RERANK_ENABLED, RERANK_TYPE, RERANK_TOP_K, RERANK_MODEL_NAME, RERANK_MAX_DOCS_FOR_LLM
)

from reranker import create_reranker, BaseReranker
from model_config_manager import model_config_manager

# 全局变量存储重排序器、向量数据库和RAG代理
global_reranker = None
global_vector_db_cache = {}  # 改为字典，按collection_name缓存
global_rag_agent = None

def get_or_create_rag_agent(debug_mode=True):
    """获取或创建全局RAG代理实例"""
    global global_rag_agent
    # 如果全局RAG代理已存在，直接返回
    if global_rag_agent is not None:
        return global_rag_agent

    print('开始创建 RAG 代理')
    # 根据debug_mode决定是否添加回调处理器
    callbacks = []
    if debug_mode:
        detailed_handler = DetailedCallbackHandler()
        callbacks.append(detailed_handler)

    # 根据当前LLM配置创建LLM实例
    try:
        llm = model_config_manager.create_llm_instance(
            callbacks=callbacks,
            verbose=debug_mode,
            temperature=0.2
        )
        llm_config = model_config_manager.get_llm_config()
        print(f'使用 {llm_config.provider} LLM: {llm_config.model_name}')
    except Exception as e:
        print(f"创建LLM实例失败: {e}")
        raise

    # 创建重排序器并设置为全局变量
    global global_reranker
    global_reranker = None
    if RERANK_ENABLED:
        try:
            print(f'启用重排序功能，类型: {RERANK_TYPE}')
            if RERANK_TYPE == "similarity":
                global_reranker = create_reranker("similarity")
            elif RERANK_TYPE == "cross_encoder":
                global_reranker = create_reranker("cross_encoder", model_name=RERANK_MODEL_NAME)
            elif RERANK_TYPE == "llm":
                global_reranker = create_reranker("llm", llm=llm, max_docs_for_llm=RERANK_MAX_DOCS_FOR_LLM)
            elif RERANK_TYPE == "hybrid":
                # 创建混合重排序器
                similarity_reranker = create_reranker("similarity")
                try:
                    cross_encoder_reranker = create_reranker("cross_encoder", model_name=RERANK_MODEL_NAME)
                    rerankers = [similarity_reranker, cross_encoder_reranker]
                    weights = [0.3, 0.7]  # 交叉编码器权重更高
                except Exception as e:
                    print(f"交叉编码器创建失败，使用相似度重排序: {str(e)}")
                    rerankers = [similarity_reranker]
                    weights = [1.0]
                global_reranker = create_reranker("hybrid", rerankers=rerankers, weights=weights)
            print('重排序器创建成功')
        except Exception as e:
            print(f'重排序器创建失败，将禁用重排序功能: {str(e)}')
            global_reranker = None

    # See full prompt at `https://smith.langchain.com/hub/langchain-ai/retrieval-qa-chat`
    retrieval_qa_chat_prompt = hub.pull("langchain-ai/retrieval-qa-chat")

    # 如果处于调试模式，打印和保存提示模板
    if debug_mode:
        print("\↓↓↓↓↓↓↓↓↓↓ 提示模板 ↓↓↓↓↓↓↓↓↓↓")
        # ChatPromptTemplate 对象没有 template 属性，改用 messages 属性
        print(retrieval_qa_chat_prompt.messages)
        print("↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")
        save_debug_data_to_file(retrieval_qa_chat_prompt.messages, "prompt_template.txt")

        # 自定义回调函数，用于打印完整的提示内容
        def debug_prompt(prompt_value):
            print("\n========== 输入给大模型的完整内容 ==========")
            print(json.dumps(prompt_value, ensure_ascii=False, indent=2))
            print("=========================================\n")
            # 保存到文件
            save_debug_data_to_file(prompt_value, "llm_input.json")

        # 添加调试钩子到提示模板
        if hasattr(retrieval_qa_chat_prompt, 'on_prompt_created'):
            retrieval_qa_chat_prompt.on_prompt_created = debug_prompt

    # 创建文档链和检索链
    combine_docs_chain = create_stuff_documents_chain(
        llm,
        retrieval_qa_chat_prompt
    )

    # 创建检索链，先使用更多文档数量以便重排序
    vector_db = get_or_create_vector_db_instance(None)
    retriever = vector_db.as_retriever(search_kwargs={'k': 5})

    rag_chain = create_retrieval_chain(
        retriever,
        combine_docs_chain
    )

    # 保存到全局变量
    global_rag_agent = rag_chain
    print('RAG 代理创建完成')
    return rag_chain

def get_or_create_vector_db_instance(texts=None, collection_name=None):
    """获取或创建向量数据库实例，支持多模型collection"""
    global global_vector_db_cache

    # 如果没有指定collection_name，使用当前embedding配置生成
    if collection_name is None:
        collection_name = model_config_manager.get_collection_name()

    # 如果缓存中已存在该collection且不需要添加新文档，直接返回
    if collection_name in global_vector_db_cache and texts is None:
        print(f'使用缓存的向量数据库实例: {collection_name}')
        return global_vector_db_cache[collection_name]

    # 根据当前embedding配置创建embedding实例
    try:
        embeddings = model_config_manager.create_embedding_instance()
        embedding_config = model_config_manager.get_embedding_config()
        print(f'使用 {embedding_config.provider} 嵌入模型: {embedding_config.model_name}')
    except Exception as e:
        print(f"创建embedding实例失败: {e}")
        raise

    if texts:
        # 如果需要添加新文档，创建新的向量数据库
        vector_db = Chroma.from_documents(
            texts,
            embeddings,
            persist_directory=CHROMA_PERSIST_DIR,
            client=HttpClient(host=CHROMA_HOST),
            collection_name=collection_name
        )
    else:
        # 否则加载现有的向量数据库
        vector_db = Chroma(
            persist_directory=CHROMA_PERSIST_DIR,
            embedding_function=embeddings,
            client=HttpClient(host=CHROMA_HOST),
            collection_name=collection_name
        )

    # 更新缓存
    global_vector_db_cache[collection_name] = vector_db
    print(f'向量数据库实例创建/更新完成: {collection_name}')
    return vector_db

# def create_vector_db_from_docs(docs_dir):
#     print('开始从文档目录创建向量数据库')
#     if docs_dir is None:
#         print('未提供文档目录，返回空列表')
#         return []
#     documents = []
#     for file in os.listdir(docs_dir):
#         if file.endswith('.pdf') or file.endswith('.txt'):
#             print(f'正在加载文件: {file}')
#             if file.endswith('.pdf'):
#                 loader = PyPDFLoader(os.path.join(docs_dir, file), mode="single")
#             elif file.endswith('.txt'):
#                 loader = TextLoader(os.path.join(docs_dir, file))
#             documents.extend(loader.load())
#     print('文档加载完成')
#     processed_texts = split_documents(documents)
#     vector_db = get_or_create_vector_db_instance(processed_texts)
#     print('从文档目录创建向量数据库完成')
#     return vector_db

#def load_web_pages(urls=None):
#    print('开始加载网页文档')
#    if urls is None:
#        print('未提供网页 URL，返回空列表')
#        return []
#    documents = []
#    for url in urls:
#        print(f'正在加载网页: {url}')
#        loader = WebBaseLoader(url)
#        documents.extend(loader.load())
#    print('网页文档加载完成')
#    return documents


def split_documents(documents):
    print('开始分割文档')
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=SPLITTER_CHUNK_SIZE, chunk_overlap=SPLITTER_CHUNK_OVERLAP)
    texts = text_splitter.split_documents(documents)
    
    # 打印分割后的文档内容和元数据
    print(f'\n========== 分割后的文档（共 {len(texts)} 块）==========')
    for i, text in enumerate(texts):
        print(f'文档块 {i+1}:')
        print(f'内容: {text.page_content[:150]}{"..." if len(text.page_content) > 150 else ""}')
        print(f'元数据: {text.metadata}')
        print('-' * 50)
    print('=' * 50)
    
    print('文档分割完成')
    return texts

def add_single_document_with_metadata(file_path=None, metadata=None):
    """
    添加单个文档并附加自定义元数据

    Args:
        file_path: PDF文件或TXT文件路径
        metadata: 要添加的自定义元数据字典

    Returns:
        vector_db: 更新后的向量数据库实例
    """
    print('开始添加单个文档')
    if file_path:
        print(f'正在加载文件: {file_path}')
        # 根据文件扩展名选择相应的加载器
        if file_path.lower().endswith('.pdf'):
            loader = PyPDFLoader(file_path)
        elif file_path.lower().endswith('.txt'):
            loader = TextLoader(file_path)
        else:
            print(f'不支持的文件类型: {file_path}')
            return None
        documents = loader.load()

        # 如果使用file_path添加文档，且元信息缺少title，则使用文件名作为title
        if metadata is None:
            metadata = {}
        if 'title' not in metadata or metadata['title'] is None or metadata['title'] == '':
            filename = os.path.basename(file_path)
            metadata['title'] = filename
            print(f'使用文件名 {filename} 作为文档标题')
    else:
        print('未提供文件路径，返回 None')
        return None

    # 添加自定义元数据
    if metadata:
        print(f'添加自定义元数据: {metadata}')
        for doc in documents:
            doc.metadata.update(metadata)

    texts = split_documents(documents)
    # 使用当前embedding配置的向量数据库实例
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    vector_db.add_documents(texts)

    print(f'单个文档添加完成，collection: {collection_name}')
    return vector_db


def add_batch_documents_with_metadata(file_paths=None, metadata=None):
    """
    批量添加文档并附加自定义元数据

    Args:
        file_paths: PDF或TXT文件路径列表
        metadata: 要添加的自定义元数据字典，将应用于所有文档

    Returns:
        vector_db: 更新后的向量数据库实例
    """
    print('开始添加批量文档')
    all_documents = []

    # 确保metadata是字典类型
    if metadata is None:
        metadata = {}

    if file_paths:
        for path in file_paths:
            print(f'正在加载文件: {path}')
            # 根据文件扩展名选择相应的加载器
            if path.lower().endswith('.pdf'):
                loader = PyPDFLoader(path)
            elif path.lower().endswith('.txt'):
                loader = TextLoader(path)
            else:
                print(f'跳过不支持的文件类型: {path}')
                continue
            documents = loader.load()

            # 为每个文件创建一个元数据副本，避免所有文档共享同一个元数据对象
            doc_metadata = metadata.copy()

            # 如果元信息缺少title，则使用文件名作为title
            if 'title' not in doc_metadata or doc_metadata['title'] is None or doc_metadata['title'] == '':
                filename = os.path.basename(path)
                doc_metadata['title'] = filename
                print(f'使用文件名 {filename} 作为文档标题')

            # 为此文件的所有文档添加元数据
            for doc in documents:
                doc.metadata.update(doc_metadata)

            all_documents.extend(documents)

    texts = split_documents(all_documents)
    # 使用当前embedding配置的向量数据库实例
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    vector_db.add_documents(texts)

    print(f'批量文档添加完成，collection: {collection_name}')
    return vector_db


def delete_single_document(document_id):
    print('开始删除单个文档')
    # 使用当前embedding配置的向量数据库实例
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    vector_db.delete([document_id])

    print(f'单个文档删除完成，collection: {collection_name}')
    return vector_db


def delete_batch_documents(document_ids):
    print('开始删除批量文档')
    # 使用当前embedding配置的向量数据库实例
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    vector_db.delete(document_ids)

    print(f'批量文档删除完成，collection: {collection_name}')
    return vector_db


def get_all_documents(title=None, page=1, page_size=50):
    """
    获取向量数据库中的所有文档信息，支持按标题过滤和分页

    Args:
        title: 可选，按标题过滤文档
        page: 页码，从1开始
        page_size: 每页文档数量

    Returns:
        documents_info: 文档信息列表
    """
    print('获取向量数据库中的所有文档')

    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    
    # 构建过滤条件
    where_filter = None
    if title:
        # ChromaDB的where过滤是直接在元数据字段上操作的
        where_filter = {"title": title}
    
    # 计算偏移量
    offset = (page - 1) * page_size
    
    try:
        # 使用ChromaDB的分页功能获取文档
        all_docs = vector_db.get(
            where=where_filter,
            limit=page_size,
            offset=offset
        )
    except Exception as e:
        print(f"ChromaDB查询错误: {str(e)}")
        # 如果过滤条件出错，尝试不使用过滤条件
        all_docs = vector_db.get(
            limit=page_size,
            offset=offset
        )
    
    print(f'获取到 {len(all_docs["ids"])} 个文档')
    
    # 构建文档信息列表
    documents_info = []
    for i in range(len(all_docs["ids"])):
        doc_info = {
            "id": all_docs["ids"][i],
            "metadata": all_docs["metadatas"][i] if all_docs["metadatas"] else {}
        }
        documents_info.append(doc_info)
    
    # 如果需要模糊匹配标题，在应用层面处理
    #if title and where_filter:
    #    filtered_docs = []
    #    for doc in documents_info:
    #        doc_title = doc.get("metadata", {}).get("title", "")
    #        if doc_title and title.lower() in doc_title.lower():
    #            filtered_docs.append(doc)
    #    documents_info = filtered_docs
    
    try:
        # 获取总文档数（用于计算总页数）
        if where_filter:
            total_docs = len(vector_db.get(where=where_filter)["ids"])
        else:
            total_docs = len(vector_db.get()["ids"])
    except Exception as e:
        print(f"获取总文档数错误: {str(e)}")
        total_docs = len(documents_info)  # 如果获取总数失败，使用当前页的文档数
    
    return {
        "documents": documents_info,
        "total": total_docs
    }


def get_document_by_id(document_id):
    """根据文档ID获取单个文档的详细信息"""
    print(f'获取ID为 {document_id} 的文档')
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)
    
    # 获取所有文档的元数据
    all_docs = vector_db.get()
    
    # 查找指定ID的文档
    if document_id in all_docs["ids"]:
        index = all_docs["ids"].index(document_id)
        doc_info = {
            "id": document_id,
            "metadata": all_docs["metadatas"][index] if all_docs["metadatas"] else {},
            "content": all_docs["documents"][index] if all_docs["documents"] else ""
        }
        print(f'找到ID为 {document_id} 的文档')
        return doc_info
    
    print(f'未找到ID为 {document_id} 的文档')
    return None


def user_query(question, debug_mode=True):
    print('开始处理用户查询')
    print(f'用户问题: {question}')

    # 根据当前embedding配置获取向量数据库
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)

    # 根据当前LLM配置创建LLM实例
    llm = model_config_manager.create_llm_instance(verbose=debug_mode, temperature=0.2)

    print(f'使用collection: {collection_name}')
    print(f'使用LLM: {model_config_manager.get_llm_config().provider} - {model_config_manager.get_llm_config().model_name}')

    # 创建token计数器
    token_counter = TokenCounter()
    callbacks = [token_counter]

    # 如果启用了调试模式，添加详细回调处理器
    if debug_mode:
        detailed_handler = DetailedCallbackHandler()
        callbacks.append(detailed_handler)

    # 使用当前配置进行查询
    try:
        # 执行相似性搜索
        docs = vector_db.similarity_search(question, k=5)

        # 构建上下文
        context = "\n\n".join([doc.page_content for doc in docs])

        # 构建提示
        prompt = f"""基于以下上下文信息回答问题。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。

上下文信息：
{context}

问题：{question}

请基于上下文提供准确的回答。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。"""

        # 调用LLM生成回答
        if hasattr(llm, "with_config"):
            # 新版本LangChain的方式
            response = llm.with_config({"callbacks": callbacks}).invoke(prompt)
        else:
            # 尝试直接传递callbacks参数
            try:
                response = llm.invoke(prompt, callbacks=callbacks)
            except:
                # 如果上述方法都失败，直接调用不使用callbacks
                print("警告: 无法使用callbacks，token统计可能不准确")
                response = llm.invoke(prompt)

        # 提取回答内容
        if hasattr(response, 'content'):
            answer = response.content
        else:
            answer = str(response)

        result = {
            "input": question,
            "output": answer,
            "context": [{"page_content": doc.page_content, "metadata": doc.metadata} for doc in docs]
        }
        
        print('用户查询处理完成')
        
        # 打印token使用情况
        print(f"Token使用情况: 提示tokens: {token_counter.prompt_tokens}, 完成tokens: {token_counter.completion_tokens}, 总tokens: {token_counter.total_tokens}")

        # 打印实际返回的结果结构
        print(f"返回结果结构: {result.keys() if isinstance(result, dict) else '非字典类型结果'}")
        if debug_mode:
            print(f"返回结果详情: {result}")
        
        # 如果处于调试模式，保存回答结果
        if debug_mode:
            save_debug_data_to_file(result, "llm_full_output.json")
            
        # 从结果中提取输出文本
        output = None
        if isinstance(result, dict):
            # 尝试从不同的键获取输出
            for key in ['answer', 'response', 'result', 'output', 'text']:
                if key in result:
                    output = result[key]
                    break
                    
            # 如果上述常见键都没找到，尝试查找包含内容的任意键
            if output is None:
                for key, value in result.items():
                    if isinstance(value, str) and len(value) > 10:  # 假设有意义的回答至少有10个字符
                        output = value
                        break
        
        # 如果所有尝试都失败，将整个结果转换为字符串
        if output is None:
            output = str(result)
            
        # 构造统一的返回结构，包含token使用情况
        return {
            "input": result.get("input", ""),
            "output": output,
            "context": result.get("context", {}),
            "token_usage": {
                "prompt_tokens": token_counter.prompt_tokens,
                "completion_tokens": token_counter.completion_tokens,
                "total_tokens": token_counter.total_tokens
            }
        }
    except Exception as e:
        print(f'处理用户查询时出错: {str(e)}')
        import traceback
        traceback.print_exc()
        return {
            "output": f"处理查询时出错: {str(e)}",
            "token_usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }

def user_query_with_rerank(question, debug_mode=True):
    """带重排序功能的用户查询函数"""
    print('开始处理用户查询（带重排序）')
    print(f'用户问题: {question}')

    # 根据当前embedding配置获取向量数据库
    collection_name = model_config_manager.get_collection_name()
    vector_db = get_or_create_vector_db_instance(None, collection_name)

    # 根据当前LLM配置创建LLM实例
    llm = model_config_manager.create_llm_instance(verbose=debug_mode, temperature=0.2)

    print(f'使用collection: {collection_name}')
    print(f'使用LLM: {model_config_manager.get_llm_config().provider} - {model_config_manager.get_llm_config().model_name}')

    # 创建token计数器
    token_counter = TokenCounter()
    callbacks = [token_counter]

    # 如果启用了调试模式，添加详细回调处理器
    if debug_mode:
        detailed_handler = DetailedCallbackHandler()
        callbacks.append(detailed_handler)

    # 检查是否启用重排序
    reranker_to_use = global_reranker
    if RERANK_ENABLED and reranker_to_use:
        try:
            print("使用重排序功能")
            # 先进行向量检索
            docs = vector_db.similarity_search(question, k=10)
            print(f"初始检索到 {len(docs)} 个文档")

            # 进行重排序
            reranked_docs = reranker_to_use.rerank(question, docs, RERANK_TOP_K)
            print(f"重排序后保留 {len(reranked_docs)} 个文档")

            # 手动构建上下文
            context = "\n\n".join([doc.page_content for doc in reranked_docs])

            # 构建提示
            prompt = f"""基于以下上下文回答问题：

上下文：
{context}

问题：{question}

请基于上下文提供准确的回答。如果上下文中没有相关信息，请说明无法从提供的信息中找到答案。"""

            # 调用LLM生成回答
            if hasattr(llm, "with_config"):
                # 新版本LangChain的方式
                response = llm.with_config({"callbacks": callbacks}).invoke(prompt)
            else:
                # 尝试直接传递callbacks参数
                try:
                    response = llm.invoke(prompt, callbacks=callbacks)
                except:
                    # 如果上述方法都失败，直接调用不使用callbacks
                    print("警告: 无法使用callbacks，token统计可能不准确")
                    response = llm.invoke(prompt)

            # 提取回答内容
            if hasattr(response, 'content'):
                answer = response.content
            else:
                answer = str(response)

            result = {"answer": answer}

            print('重排序查询处理完成')

            # 构造返回结构
            return {
                "output": result.get("answer", str(result)),
                "context": reranked_docs,
                "token_usage": {
                    "prompt_tokens": token_counter.prompt_tokens,
                    "completion_tokens": token_counter.completion_tokens,
                    "total_tokens": token_counter.total_tokens
                }
            }

        except Exception as e:
            print(f'重排序查询失败，回退到标准查询: {str(e)}')
            # 回退到标准查询
            return user_query(question, debug_mode)
    else:
        # 使用标准查询
        return user_query(question, debug_mode)




########################################################################################
# 自定义回调处理器，用于详细记录LLM输入输出
class DetailedCallbackHandler(BaseCallbackHandler):
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs):
        """当LLM开始处理时调用"""
        print("\n↓↓↓↓↓↓↓↓↓↓ LLM 输入内容 ↓↓↓↓↓↓↓↓↓↓")
        if prompts:
            for i, prompt in enumerate(prompts):
                print(f"提示 {i+1}:")
                print(prompt)
        else:
            print("没有提示内容")
        print("↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")

    def on_llm_end(self, response, **kwargs):
        """当LLM完成处理时调用"""
        print("\n↓↓↓↓↓↓↓↓↓↓ LLM 输出内容 ↓↓↓↓↓↓↓↓↓↓")
        try:
            if response and hasattr(response, 'generations') and response.generations:
                print(response.generations[0][0].text)
            else:
                print(f"无法解析的响应: {str(response)}")
        except Exception as e:
            print(f"处理LLM响应时出错: {str(e)}")
            print(f"原始响应: {str(response)}")
        print("↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")

    def on_chain_start(self, serialized: Dict[str, Any], inputs: Dict[str, Any], **kwargs):
        return
        """当链开始处理时调用"""
        if serialized is None:
            chain_type = "<unknown>"
        else:
            chain_type = serialized.get("id", ["<unknown>"])[-1]
        print(f"\n========== 链开始: {chain_type} ==========")
        try:
            # 处理Document对象
            processed_inputs = {}
            for key, value in inputs.items():
                if hasattr(value, 'page_content') and hasattr(value, 'metadata'):
                    # 处理单个Document对象
                    processed_inputs[key] = {
                        "page_content": value.page_content,
                        "metadata": value.metadata
                    }
                elif isinstance(value, list):
                    # 处理Document列表
                    processed_list = []
                    for item in value:
                        if hasattr(item, 'page_content') and hasattr(item, 'metadata'):
                            processed_list.append({
                                "page_content": item.page_content,
                                "metadata": item.metadata
                            })
                        else:
                            processed_list.append(str(item))
                    processed_inputs[key] = processed_list
                else:
                    processed_inputs[key] = value
            
            print(f"输入: {json.dumps(processed_inputs, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"无法序列化输入: {str(e)}")
            print(f"原始输入: {str(inputs)}")
        print("==================================\n")

    def on_chain_end(self, outputs: Dict[str, Any], **kwargs):
        return
        """当链完成处理时调用"""
        print("\n========== 链结束 ==========")
        try:
            # 处理Document对象
            processed_outputs = {}
            for key, value in outputs.items():
                if hasattr(value, 'page_content') and hasattr(value, 'metadata'):
                    # 处理单个Document对象
                    processed_outputs[key] = {
                        "page_content": value.page_content,
                        "metadata": value.metadata
                    }
                elif isinstance(value, list):
                    # 处理Document列表
                    processed_list = []
                    for item in value:
                        if hasattr(item, 'page_content') and hasattr(item, 'metadata'):
                            processed_list.append({
                                "page_content": item.page_content,
                                "metadata": item.metadata
                            })
                        else:
                            processed_list.append(str(item))
                    processed_outputs[key] = processed_list
                else:
                    processed_outputs[key] = value
            
            print(f"输出: {json.dumps(processed_outputs, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"无法序列化输出: {str(e)}")
            print(f"原始输出: {str(outputs)}")
        print("==================================\n")

    def on_retriever_start(self, serialized: Dict[str, Any], query: str, **kwargs):
        """当检索器开始处理时调用"""
        print(f"\n↓↓↓↓↓↓↓↓↓↓ 检索开始 ↓↓↓↓↓↓↓↓↓↓")
        print(f"查询: {query}")
        print("↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")

    def on_retriever_end(self, documents, **kwargs):
        """当检索器完成处理时调用"""
        print(f"\n↓↓↓↓↓↓↓↓↓↓ 检索结果 ↓↓↓↓↓↓↓↓↓↓")
        if documents:
            for i, doc in enumerate(documents):
                print(f"文档 {i+1}: {doc.page_content[:100]}...")
                print(f"元数据: {doc.metadata}")
                print("---")
        else:
            print("没有检索到文档")
        print("↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")


# Token计数器回调类
class TokenCounter(BaseCallbackHandler):
    def __init__(self):
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0
        
    def on_llm_start(self, serialized, prompts, **kwargs):
        """计算提示的token数量"""
        if LLM_PROVIDER.lower() == "gemini":
            # Gemini模型使用近似计算
            self.prompt_tokens = 0  # 重置提示tokens计数
            for prompt in prompts:
                # 使用tiktoken估算token数量
                try:
                    encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
                    tokens = len(encoding.encode(prompt))
                    self.prompt_tokens += tokens
                    print(f"Gemini 提示token估算: {tokens}")
                except Exception as e:
                    print(f"Token计数错误: {str(e)}")
                    # 如果tiktoken失败，使用字符数/4的粗略估计
                    tokens = len(prompt) // 4
                    self.prompt_tokens += tokens
                    print(f"Gemini 提示token粗略估算: {tokens}")
        else:
            # Ollama模型的token计数在on_llm_end中处理
            pass
            
    def on_llm_end(self, response, **kwargs):
        """计算完成的token数量"""
        if LLM_PROVIDER.lower() == "gemini":
            # 从Gemini响应中提取token使用情况
            try:
                # 首先尝试从generations中的usage_metadata获取精确统计
                if (hasattr(response, 'generations') and response.generations and
                    len(response.generations) > 0 and len(response.generations[0]) > 0):
                    generation = response.generations[0][0]
                    if (hasattr(generation, 'message') and
                        hasattr(generation.message, 'usage_metadata') and
                        generation.message.usage_metadata):
                        usage_metadata = generation.message.usage_metadata
                        print(f"Gemini token使用情况(来自usage_metadata): {usage_metadata}")
                        self.prompt_tokens = usage_metadata.get("input_tokens", self.prompt_tokens)
                        self.completion_tokens = usage_metadata.get("output_tokens", 0)
                        self.total_tokens = usage_metadata.get("total_tokens", 0) or (self.prompt_tokens + self.completion_tokens)
                        return

                # 备用方案：从llm_output.usage获取
                elif hasattr(response, "llm_output") and response.llm_output and response.llm_output.get("usage"):
                    usage = response.llm_output.get("usage", {})
                    print(f"Gemini token使用情况(来自llm_output.usage): {usage}")
                    self.prompt_tokens = usage.get("prompt_tokens", self.prompt_tokens)
                    self.completion_tokens = usage.get("completion_tokens", 0)
                    self.total_tokens = usage.get("total_tokens", 0) or (self.prompt_tokens + self.completion_tokens)
                
                else:
                    # 最后备用方案：使用tiktoken估算
                    print(f"Gemini token使用情况(使用tiktoken估算)")
                    text = response.generations[0][0].text
                    try:
                        encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
                        self.completion_tokens = len(encoding.encode(text))
                    except:
                        print(f"无法使用tiktoken估算，使用字符数/4的粗略估计")
                        self.completion_tokens = len(text) // 4
                    self.total_tokens = self.prompt_tokens + self.completion_tokens
            except Exception as e:
                print(f"Gemini token计数错误: {str(e)}")
                # 使用字符数/4的粗略估计
                try:
                    text = response.generations[0][0].text
                    self.completion_tokens = len(text) // 4
                    self.total_tokens = self.prompt_tokens + self.completion_tokens
                except:
                    print("无法获取响应文本，统计token使用默认值")
                    self.completion_tokens = 0
                    self.total_tokens = self.prompt_tokens
        else:
            # Ollama模型的token计数
            try:
                # 首先尝试从generation_info中获取token信息
                if (response.generations and
                    len(response.generations) > 0 and
                    len(response.generations[0]) > 0 and
                    hasattr(response.generations[0][0], 'generation_info') and
                    response.generations[0][0].generation_info):

                    generation_info = response.generations[0][0].generation_info
                    print(f"Ollama token使用情况(来自generation_info): {generation_info}")

                    self.prompt_tokens = generation_info.get("prompt_eval_count", 0)
                    self.completion_tokens = generation_info.get("eval_count", 0)
                    self.total_tokens = self.prompt_tokens + self.completion_tokens

                # 备用方案：从llm_output获取
                elif hasattr(response, "llm_output") and response.llm_output:
                    print(f"Ollama token使用情况(来自llm_output): {response.llm_output}")
                    ollama_stats = response.llm_output
                    self.prompt_tokens = ollama_stats.get("prompt_eval_count", 0)
                    self.completion_tokens = ollama_stats.get("eval_count", 0)
                    self.total_tokens = self.prompt_tokens + self.completion_tokens

                else:
                    print(f"无法获取Ollama的token使用情况, 使用粗略估算")
                    # 使用粗略估算
                    if response.generations and len(response.generations) > 0 and len(response.generations[0]) > 0:
                        text = response.generations[0][0].text
                        self.completion_tokens = len(text) // 4  # 粗略估算
                        self.total_tokens = self.prompt_tokens + self.completion_tokens
                    else:
                        self.completion_tokens = 0
                        self.total_tokens = self.prompt_tokens

            except Exception as e:
                print(f"Ollama token计数错误: {str(e)}")
                # 错误情况下的默认处理
                self.completion_tokens = 0
                self.total_tokens = self.prompt_tokens


class RAGAgentWrapper:
    """RAG代理包装类，用于存储重排序器"""
    def __init__(self, rag_chain, reranker=None):
        self.rag_chain = rag_chain
        self.reranker = reranker

    def invoke(self, *args, **kwargs):
        """代理调用原始链的invoke方法"""
        return self.rag_chain.invoke(*args, **kwargs)

    def with_config(self, *args, **kwargs):
        """代理调用原始链的with_config方法"""
        return self.rag_chain.with_config(*args, **kwargs)

    def __getattr__(self, name):
        """代理访问原始链的其他属性和方法"""
        return getattr(self.rag_chain, name)


# 保存调试数据到文件
def save_debug_data_to_file(data, filename="debug_data.txt"):
    """将调试数据保存到文件中，方便查看完整内容"""
    try:
        with open(filename, "w", encoding="utf-8") as f:
            processed_data = process_for_serialization(data)
            if isinstance(processed_data, (dict, list)):
                f.write(json.dumps(processed_data, ensure_ascii=False, indent=2))
            else:
                f.write(str(processed_data))
        print(f"调试数据已保存到文件: {filename}")
    except Exception as e:
        print(f"保存调试数据到文件时出错: {str(e)}")
        # 在严重错误时退回到简单字符串保存方式
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(str(data))
            print(f"使用字符串格式保存成功: {filename}")
        except:
            print("退回保存方式也失败了，无法保存文件")


def process_for_serialization(obj):
    """处理对象使其可以被JSON序列化，特别处理Document对象"""
    if hasattr(obj, 'page_content') and hasattr(obj, 'metadata'):
        # 处理Document对象
        return {
            "page_content": obj.page_content,
            "metadata": obj.metadata
        }
    elif isinstance(obj, dict):
        # 处理字典
        return {k: process_for_serialization(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        # 处理列表
        return [process_for_serialization(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        # 处理其他对象
        try:
            return process_for_serialization(obj.__dict__)
        except:
            return str(obj)
    else:
        # 基本类型或其他类型
        try:
            # 尝试直接返回，如果不可序列化会在调用处被捕获
            json.dumps(obj)
            return obj
        except:
            return str(obj)

def main():      
    # 设置调试模式
    debug_mode = True
 
    # 测试调试日志功能，使用我们统一的输出格式
 #   print("\n===== 测试1: 普通问题 =====")
 #   question1 = 'What are the key architectural changes in Aave V2 compared to V1?'
 #   answer1 = user_query(agent, question1, debug_mode=debug_mode)
 #   print(f"\n回答: {answer1['output']}")
    
 #   print("\n===== 测试2: 关于模型自身的问题 =====")
 #   question2 = '你是什么模型？'
 #   answer2 = user_query(agent, question2, debug_mode=debug_mode)
 #   print(f"\n回答: {answer2['output']}")
    
 #   print("\n===== 测试3: 带有上下文的问题 =====")
 #   question3 = '请解释Uniswap V3的主要创新点'
 #   answer3 = user_query(agent, question3, debug_mode=debug_mode)
 #   print(f"\n回答: {answer3['output']}")

    print("\n===== 测试4: 私有问题 =====")
    #add_single_document_with_metadata(file_path="datas/test.txt", metadata={"title": "test"})
    question4 = 'Does KFC have salads?'
    answer4 = user_query(question4, debug_mode=debug_mode)
    print(f"\n回答: {answer4['output']}")

if __name__ == '__main__':
    main()
    print('hissssl')
#!/usr/bin/env python
"""
测试TokenCounter是否正确根据当前LLM配置进行token统计
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_config_manager import model_config_manager

def test_token_counter_with_different_llm_configs():
    """测试TokenCounter在不同LLM配置下的行为"""
    
    print("=== 测试TokenCounter与模型配置管理器的集成 ===")
    
    # 1. 测试获取当前配置
    print("\n1. 获取当前LLM配置:")
    current_config = model_config_manager.get_llm_config()
    print(f"   Provider: {current_config.provider}")
    print(f"   Model: {current_config.model_name}")
    
    # 2. 测试切换到ollama配置
    print("\n2. 切换到Ollama LLM配置:")
    success = model_config_manager.set_llm_config(
        provider="ollama",
        model_name="llama3.2:3b",
        base_url="http://10.1.177.121:11434/"
    )
    print(f"   切换成功: {success}")
    
    if success:
        new_config = model_config_manager.get_llm_config()
        print(f"   新配置 - Provider: {new_config.provider}")
        print(f"   新配置 - Model: {new_config.model_name}")
    
    # 3. 测试切换到gemini配置
    print("\n3. 切换到Gemini LLM配置:")
    success = model_config_manager.set_llm_config(
        provider="gemini",
        model_name="gemini-2.0-flash",
        api_key="test-api-key"
    )
    print(f"   切换成功: {success}")
    
    if success:
        new_config = model_config_manager.get_llm_config()
        print(f"   新配置 - Provider: {new_config.provider}")
        print(f"   新配置 - Model: {new_config.model_name}")
    
    # 4. 测试TokenCounter类的配置感知
    print("\n4. 测试TokenCounter的配置感知:")
    
    try:
        # 导入TokenCounter类
        from rag_agent import TokenCounter
        
        # 创建TokenCounter实例
        token_counter = TokenCounter()
        
        # 模拟on_llm_start调用
        print("   模拟TokenCounter.on_llm_start调用...")
        test_prompts = ["这是一个测试提示"]
        
        # 测试ollama配置
        model_config_manager.set_llm_config(
            provider="ollama",
            model_name="llama3.2:3b",
            base_url="http://10.1.177.121:11434/"
        )
        print(f"   当前LLM配置: {model_config_manager.get_llm_config().provider}")
        token_counter.on_llm_start({}, test_prompts)
        print(f"   Ollama配置下的prompt_tokens: {token_counter.prompt_tokens}")
        
        # 重置计数器
        token_counter.prompt_tokens = 0
        token_counter.completion_tokens = 0
        token_counter.total_tokens = 0
        
        # 测试gemini配置
        model_config_manager.set_llm_config(
            provider="gemini",
            model_name="gemini-2.0-flash",
            api_key="test-api-key"
        )
        print(f"   当前LLM配置: {model_config_manager.get_llm_config().provider}")
        token_counter.on_llm_start({}, test_prompts)
        print(f"   Gemini配置下的prompt_tokens: {token_counter.prompt_tokens}")
        
        print("   ✅ TokenCounter成功根据当前LLM配置进行token统计")
        
    except Exception as e:
        print(f"   ❌ TokenCounter测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 测试embedding配置
    print("\n5. 测试Embedding配置:")
    
    # 测试ollama embedding
    success = model_config_manager.set_embedding_config(
        provider="ollama",
        model_name="llama3.2:3b",
        base_url="http://10.1.177.121:11434/"
    )
    print(f"   设置Ollama embedding: {success}")
    if success:
        collection_name = model_config_manager.get_collection_name()
        print(f"   Collection名称: {collection_name}")
    
    # 测试gemini embedding
    success = model_config_manager.set_embedding_config(
        provider="gemini",
        model_name="models/gemini-embedding-exp-03-07",
        api_key="test-api-key"
    )
    print(f"   设置Gemini embedding: {success}")
    if success:
        collection_name = model_config_manager.get_collection_name()
        print(f"   Collection名称: {collection_name}")
    
    print("\n=== 测试完成 ===")

def test_config_info():
    """测试配置信息获取"""
    print("\n=== 测试配置信息获取 ===")
    
    config_info = model_config_manager.get_config_info()
    print("当前完整配置信息:")
    import json
    print(json.dumps(config_info, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_token_counter_with_different_llm_configs()
    test_config_info()

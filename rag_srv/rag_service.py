#!/usr/bin/env python
from fastapi import FastAPI, HTTPException, Body
from dapr.ext.fastapi import <PERSON>pr<PERSON><PERSON>
import uvicorn
from typing import List, Dict, Optional, Any
import os
import time
import logging
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
from config import DEBUG_MODE

# 导入原有 rag_agent.py 中的函数
from rag_agent import (
    add_single_document_with_metadata,
    add_batch_documents_with_metadata,
    delete_single_document,
    delete_batch_documents,
    get_all_documents,
    get_document_by_id,
    user_query,
    user_query_with_rerank
)

# 导入模型配置管理器
from model_config_manager import model_config_manager

# 配置日志
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("rag_service.log")
    ]
)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(title="RAG Service", description="RAG 服务，用于文档检索和问答")
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)
dapr_app = DaprApp(app)

# 请求模型定义
class DocumentRequest(BaseModel):
    file_path: str
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    
class BatchDocumentsRequest(BaseModel):
    file_paths: List[str]
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None

class UrlRequest(BaseModel):
    url: str
    
class BatchUrlsRequest(BaseModel):
    urls: List[str]

class QueryRequest(BaseModel):
    question: str

class DocumentIdRequest(BaseModel):
    document_id: str
    
class BatchDocumentIdsRequest(BaseModel):
    document_ids: List[str]

class PaginationParams(BaseModel):
    page: int = 1
    page_size: int = 10

# 模型配置相关的请求模型
class EmbeddingConfigRequest(BaseModel):
    provider: str  # 'ollama' 或 'gemini'
    model_name: str
    base_url: Optional[str] = None  # 仅ollama需要
    api_key: Optional[str] = None   # 仅gemini需要

class LLMConfigRequest(BaseModel):
    provider: str  # 'ollama' 或 'gemini'
    model_name: str
    base_url: Optional[str] = None  # 仅ollama需要
    api_key: Optional[str] = None   # 仅gemini需要

# 添加单个 PDF 文档接口
@app.post("/documents")
async def add_document(doc_request: DocumentRequest = Body(...)):
    """添加单个 PDF 文档到向量库"""
    file_path = doc_request.file_path
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return {"success": False, "message": "文件不存在", "error": f"文件路径: {file_path}"}
    
    # 检查文件类型
    if not (file_path.endswith('.pdf') or file_path.endswith('.txt')):
        return {"success": False, "message": "不支持的文件类型", "error": "仅支持 PDF 和 TXT 文件"}
    
    try:
        # 构建自定义元数据
        custom_metadata = {}
        if doc_request.title is not None:
            custom_metadata["title"] = doc_request.title
        if doc_request.author is not None:
            custom_metadata["author"] = doc_request.author
        if doc_request.subject is not None:
            custom_metadata["subject"] = doc_request.subject
        
        add_single_document_with_metadata(file_path, custom_metadata)

        return {"success": True, "message": "文档添加成功", "data": {"file_path": file_path, "metadata": custom_metadata if custom_metadata else None}}
    except Exception as e:
        return {"success": False, "message": "添加文档失败", "error": str(e)}

# 添加批量 PDF 文档接口
@app.post("/documents/batch")
async def add_batch_docs(batch_request: BatchDocumentsRequest = Body(...)):
    """批量添加 PDF 文档到向量库"""
    file_paths = batch_request.file_paths
    
    # 检查文件是否都存在
    invalid_files = []
    for path in file_paths:
        if not os.path.exists(path):
            invalid_files.append({"path": path, "reason": "文件不存在"})
        elif not path.endswith('.pdf'):
            invalid_files.append({"path": path, "reason": "不支持的文件类型"})
    
    if invalid_files:
        return {"success": False, "message": "存在无效文件", "error": invalid_files}
    
    try:
        # 构建自定义元数据
        custom_metadata = {}
        if batch_request.title is not None:
            custom_metadata["title"] = batch_request.title
        if batch_request.author is not None:
            custom_metadata["author"] = batch_request.author
        if batch_request.subject is not None:
            custom_metadata["subject"] = batch_request.subject
            
        add_batch_documents_with_metadata(file_paths, custom_metadata if custom_metadata else None)

        return {"success": True, "message": "批量添加文档成功", "data": {"count": len(file_paths), "files": file_paths, "metadata": custom_metadata if custom_metadata else None}}
    except Exception as e:
        return {"success": False, "message": "批量添加文档失败", "error": str(e)}

# 删除文档接口
@app.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """删除单个文档"""
    try:
        # 删除文档
        delete_single_document(document_id)

        return {"success": True, "message": "文档删除成功", "data": {"document_id": document_id}}
    except Exception as e:
        return {"success": False, "message": "删除文档失败", "error": str(e)}

# 批量删除文档接口
@app.delete("/batch-documents")
async def delete_batch_docs(delete_request: BatchDocumentIdsRequest = Body(...)):
    """批量删除文档"""
    document_ids = delete_request.document_ids
    
    try:
        # 批量删除文档
        delete_batch_documents(document_ids)

        return {"success": True, "message": "批量删除文档成功", "data": {"count": len(document_ids), "document_ids": document_ids}}
    except Exception as e:
        return {"success": False, "message": "批量删除文档失败", "error": str(e)}

# 获取所有文档列表接口
@app.get("/documents")
async def list_documents(page: int = 1, page_size: int = 50, title: Optional[str] = None):
    """
    获取向量数据库中的所有文档列表，支持分页和按标题筛选
    
    优化点：
    1. 使用ChromaDB的分页功能(limit/offset)在数据库层面实现分页，避免加载所有文档到内存
    2. 尝试使用ChromaDB的元数据过滤功能，但由于ChromaDB只支持精确匹配，模糊匹配仍在应用层处理
    3. 优化了错误处理，确保即使过滤条件出错也能返回结果
    """
    try:
        # 使用更新后的get_all_documents函数，直接在数据库层面进行过滤和分页
        result = get_all_documents(title=title, page=page, page_size=page_size)
        documents = result["documents"]
        total_documents = result["total"]
        
        # 计算总页数
        total_pages = (total_documents + page_size - 1) // page_size
        
        return {
            "success": True, 
            "message": "获取文档列表成功", 
            "data": {
                "count": total_documents, 
                "documents": documents,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_pages": total_pages
                }
            }
        }
    except Exception as e:
        return {"success": False, "message": "获取文档列表失败", "error": str(e)}

# 获取单个文档详情接口
@app.get("/documents/{document_id}")
async def get_document_detail(document_id: str):
    """获取单个文档的详细信息"""
    try:
        # 获取指定ID的文档
        document = get_document_by_id(document_id)
        
        if document is None:
            return {"success": False, "message": "文档不存在", "error": f"未找到ID为 {document_id} 的文档"}
        
        return {
            "success": True, 
            "message": "获取文档详情成功", 
            "data": document
        }
    except Exception as e:
        return {"success": False, "message": "获取文档详情失败", "error": str(e)}

# 问答接口
@app.post("/query")
async def query(query_request: QueryRequest = Body(...)):
    """处理用户查询"""
    start_time = time.time()
    question = query_request.question
    logger.info(f"收到用户查询: {question}")

    try:
        # 处理查询 - 优先使用重排序功能
        from config import RERANK_ENABLED
        if RERANK_ENABLED:
            logger.info("使用重排序查询")
            result = user_query_with_rerank(question, debug_mode=DEBUG_MODE)
        else:
            logger.info("使用标准查询")
            result = user_query(question, debug_mode=DEBUG_MODE)
        
        # 提取token使用情况
        token_usage = result.get("token_usage", {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        })
        
        # 构建响应
        response = {
            "success": True, 
            "message": "查询成功", 
            "data": {
                "answer": result["output"], 
                "context": result.get("context", []),
                "input": result.get("input", question),
                "processing_time": f"{time.time() - start_time:.2f}秒",
                "token_usage": token_usage
            }
        }
        
        logger.info(f"查询处理完成，用时: {response['data']['processing_time']}")
        logger.info(f"Token使用情况: {token_usage}")
        
        return response
    except Exception as e:
        logger.error(f"处理查询时出错: {str(e)}")
        return {"success": False, "message": "查询处理失败", "error": str(e)}

# 健康检查接口
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok"}


# Dapr 服务发布/订阅功能 - 接收文档更新通知
# @dapr_app.subscribe(pubsub="pubsub", topic="document-updates")
# async def document_update_handler(data: Dict[str, Any]):
#     """处理文档更新事件"""
#     operation = data.get("operation")
    
#     if operation == "add":
#         file_path = data.get("file_path")
#         if file_path:
#             try:
#                 add_single_document(file_path=file_path)
#                 return {"success": True, "message": "通过事件添加文档成功", "data": {"file_path": file_path}}
#             except Exception as e:
#                 return {"success": False, "message": "通过事件添加文档失败", "error": str(e)}
    
#     return {"success": False, "message": "不支持的操作", "error": f"未知操作类型: {operation}"}

# # 状态检查接口
# @app.get("/status")
# async def get_status():
#     global rag_agent_instance
#     if rag_agent_instance is None:
#         if initialize_rag_agent():
#             return {"success": True, "message": "初始化成功", "data": {"agent_ready": True}}
#         return {"success": True, "message": "未初始化", "data": {"agent_ready": False}}
#     return {"success": True, "message": "服务就绪", "data": {"agent_ready": True}}

# Dapr 状态管理示例 - 保存服务状态
# @app.post("/save-state")
# async def save_state():
#     """保存服务状态"""
#     state = {"initialized": rag_agent_instance is not None}
    
#     try:
#         # 使用 Dapr 客户端保存状态
#         await dapr_app.state.save_state(
#             store_name="statestore",
#             key="rag_service_state",
#             value=state
#         )
        
#         return {"success": True, "message": "状态保存成功", "data": state}
#     except Exception as e:
#         return {"success": False, "message": "状态保存失败", "error": str(e)}

# ===== 模型配置管理API端点 =====

@app.get("/config/models")
async def get_model_config():
    """获取当前模型配置信息"""
    try:
        config_info = model_config_manager.get_config_info()
        return {
            "success": True,
            "message": "获取模型配置成功",
            "data": config_info
        }
    except Exception as e:
        return {
            "success": False,
            "message": "获取模型配置失败",
            "error": str(e)
        }

@app.post("/config/embedding")
async def set_embedding_config(config_request: EmbeddingConfigRequest = Body(...)):
    """设置embedding模型配置"""
    try:
        success = model_config_manager.set_embedding_config(
            provider=config_request.provider,
            model_name=config_request.model_name,
            base_url=config_request.base_url,
            api_key=config_request.api_key
        )

        if success:
            # 清除向量数据库缓存，强制重新创建
            import rag_agent
            rag_agent.global_vector_db_cache.clear()

            config_info = model_config_manager.get_config_info()
            return {
                "success": True,
                "message": "Embedding模型配置更新成功",
                "data": {
                    "new_config": config_info["embedding"],
                    "note": "向量数据库缓存已清除，下次操作将使用新的embedding模型"
                }
            }
        else:
            return {
                "success": False,
                "message": "Embedding模型配置更新失败"
            }
    except Exception as e:
        return {
            "success": False,
            "message": "设置Embedding模型配置失败",
            "error": str(e)
        }

@app.post("/config/llm")
async def set_llm_config(config_request: LLMConfigRequest = Body(...)):
    """设置LLM模型配置"""
    try:
        success = model_config_manager.set_llm_config(
            provider=config_request.provider,
            model_name=config_request.model_name,
            base_url=config_request.base_url,
            api_key=config_request.api_key
        )

        if success:
            # 清除RAG agent缓存，强制重新创建
            import rag_agent
            rag_agent.global_rag_agent = None

            config_info = model_config_manager.get_config_info()
            return {
                "success": True,
                "message": "LLM模型配置更新成功",
                "data": {
                    "new_config": config_info["llm"],
                    "note": "RAG agent缓存已清除，下次查询将使用新的LLM模型"
                }
            }
        else:
            return {
                "success": False,
                "message": "LLM模型配置更新失败"
            }
    except Exception as e:
        return {
            "success": False,
            "message": "设置LLM模型配置失败",
            "error": str(e)
        }

@app.get("/config/collections")
async def list_collections():
    """列出所有可用的向量数据库collection"""
    try:
        from rag_agent import global_vector_db_cache
        collections = list(global_vector_db_cache.keys())

        # 获取当前使用的collection
        current_collection = model_config_manager.get_collection_name()

        return {
            "success": True,
            "message": "获取collection列表成功",
            "data": {
                "current_collection": current_collection,
                "available_collections": collections,
                "total_collections": len(collections)
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": "获取collection列表失败",
            "error": str(e)
        }

# 主函数
if __name__ == "__main__":
    # 获取端口，优先从环境变量中获取，否则使用默认值8080
    port = int(os.getenv("DAPR_APP_PORT", 8080))

    # 启动服务
    uvicorn.run(
        "rag_service:app", 
        host="0.0.0.0", 
        port=port, 
        reload=DEBUG_MODE
    )
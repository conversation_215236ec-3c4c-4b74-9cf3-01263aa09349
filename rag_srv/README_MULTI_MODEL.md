# 多模型RAG系统使用指南

## 概述

本系统实现了方案1：多模型向量存储的RAG系统，支持：

1. **独立配置embedding模型和LLM模型**：可以分别设置用于文档向量化的embedding模型和用于回答生成的LLM模型
2. **多模型向量存储**：为每个embedding模型维护独立的向量集合(collection)
3. **运行时动态切换**：提供API端点支持运行时切换模型配置
4. **向量空间隔离**：避免不同embedding模型之间的向量空间不匹配问题

## 核心特性

### 1. 独立的模型配置
- **Embedding模型**：负责将文档转换为向量，用于文档存储和检索
- **LLM模型**：负责根据检索到的上下文生成回答

### 2. 多Collection支持
- 每个embedding模型对应一个独立的ChromaDB collection
- Collection命名规则：`documents_{provider}_{model_name}`
- 例如：`documents_ollama_llama3_2_3b`、`documents_gemini_models_gemini_embedding_exp_03_07`

### 3. 运行时配置切换
- 支持通过API动态切换embedding和LLM模型
- 切换后立即生效，无需重启服务

## API端点

### 模型配置管理

#### 1. 获取当前模型配置
```http
GET /config/models
```

响应示例：
```json
{
  "success": true,
  "message": "获取模型配置成功",
  "data": {
    "embedding": {
      "provider": "ollama",
      "model_name": "llama3.2:3b",
      "collection_name": "documents_ollama_llama3_2_3b"
    },
    "llm": {
      "provider": "gemini",
      "model_name": "gemini-2.0-flash"
    }
  }
}
```

#### 2. 设置Embedding模型配置
```http
POST /config/embedding
Content-Type: application/json

{
  "provider": "ollama",
  "model_name": "llama3.2:3b",
  "base_url": "http://10.1.177.121:11434/"
}
```

或者设置Gemini embedding：
```json
{
  "provider": "gemini",
  "model_name": "models/gemini-embedding-exp-03-07",
  "api_key": "your-api-key"
}
```

#### 3. 设置LLM模型配置
```http
POST /config/llm
Content-Type: application/json

{
  "provider": "gemini",
  "model_name": "gemini-2.0-flash",
  "api_key": "your-api-key"
}
```

或者设置Ollama LLM：
```json
{
  "provider": "ollama",
  "model_name": "llama3.2:3b",
  "base_url": "http://10.1.177.121:11434/"
}
```

#### 4. 列出所有Collections
```http
GET /config/collections
```

响应示例：
```json
{
  "success": true,
  "message": "获取collection列表成功",
  "data": {
    "current_collection": "documents_ollama_llama3_2_3b",
    "available_collections": [
      "documents_ollama_llama3_2_3b",
      "documents_gemini_models_gemini_embedding_exp_03_07"
    ],
    "total_collections": 2
  }
}
```

## 使用场景

### 场景1：比较不同Embedding模型的效果
1. 使用ollama embedding添加文档
2. 切换到gemini embedding，重新添加相同文档
3. 分别查询两个collection，比较检索效果

### 场景2：混合使用不同模型
1. 使用高质量的embedding模型（如Gemini）进行文档向量化
2. 使用本地LLM模型（如Ollama）进行回答生成
3. 既保证检索质量，又保护数据隐私

### 场景3：模型迁移
1. 原有文档使用旧embedding模型
2. 新文档使用新embedding模型
3. 系统可以同时支持两种模型的查询

## 配置文件说明

### config.py 新增配置项

```python
# ===== EMBEDDING 模型配置 =====
EMBEDDING_PROVIDER = os.environ.get('EMBEDDING_PROVIDER', 'gemini')

# ===== LLM 模型配置 =====  
LLM_PROVIDER = os.environ.get('LLM_PROVIDER', 'gemini')

# ===== Ollama配置 =====
OLLAMA_EMBEDDING_MODEL = os.environ.get('OLLAMA_EMBEDDING_MODEL', 'llama3.2:3b')
OLLAMA_LLM_MODEL = os.environ.get('OLLAMA_LLM_MODEL', 'llama3.2:3b')

# ===== Google Gemini配置 =====
GOOGLE_EMBEDDING_MODEL = os.environ.get('GOOGLE_EMBEDDING_MODEL', 'models/gemini-embedding-exp-03-07')
GOOGLE_LLM_MODEL = os.environ.get('GOOGLE_LLM_MODEL', 'gemini-2.0-flash')
```

## 测试脚本

运行测试脚本验证功能：

```bash
cd rag_srv
python test_multi_model.py
```

测试脚本会：
1. 获取当前配置
2. 切换embedding和LLM模型
3. 添加文档到不同collection
4. 测试跨模型查询
5. 验证collection隔离

## 注意事项

1. **向量空间隔离**：不同embedding模型的向量不能混用，系统会自动为每个模型创建独立的collection
2. **缓存清理**：切换embedding模型后，向量数据库缓存会自动清除
3. **API密钥安全**：生产环境中请通过环境变量设置API密钥，避免硬编码
4. **性能考虑**：每个embedding模型都需要存储一份文档向量，会增加存储空间

## 故障排除

### 1. 模型切换后查询失败
- 检查新模型配置是否正确
- 确认API密钥或base_url设置正确
- 查看服务日志获取详细错误信息

### 2. Collection不存在
- 确认已使用当前embedding模型添加过文档
- 检查collection命名是否正确

### 3. 向量检索结果为空
- 确认查询使用的collection中有相关文档
- 尝试调整查询关键词或增加文档数量

import os

# ===== EMBEDDING 模型配置 =====
# Embedding提供商选择（gemini或ollama）
EMBEDDING_PROVIDER = os.environ.get('EMBEDDING_PROVIDER', 'gemini')

# ===== LLM 模型配置 =====
# LLM提供商选择（gemini或ollama）
LLM_PROVIDER = os.environ.get('LLM_PROVIDER', 'gemini')

# ===== Ollama配置 =====
OLLAMA_BASE_URL = os.environ.get('OLLAMA_BASE_URL', 'http://************:11434/')
# Ollama Embedding模型
OLLAMA_EMBEDDING_MODEL = os.environ.get('OLLAMA_EMBEDDING_MODEL', 'llama3.2:3b')
# Ollama LLM模型
OLLAMA_LLM_MODEL = os.environ.get('OLLAMA_LLM_MODEL', 'llama3.2:3b')

# ===== Google Gemini配置 =====
GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY', 'AIzaSyA7-ue1fLWSi1W59QFiSqeU5GXQdFp964c')
# Gemini Embedding模型
GOOGLE_EMBEDDING_MODEL = os.environ.get('GOOGLE_EMBEDDING_MODEL', 'models/gemini-embedding-exp-03-07')
# Gemini LLM模型
GOOGLE_LLM_MODEL = os.environ.get('GOOGLE_LLM_MODEL', 'gemini-2.0-flash')

# ChromaDB配置
CHROMA_PERSIST_DIR = os.environ.get('CHROMA_PERSIST_DIR', '/home/<USER>/chroma_space')
CHROMA_HOST = os.environ.get('CHROMA_HOST', '************')
CHROMA_PORT = os.environ.get('CHROMA_PORT', '8000')

# 文本分割器配置
SPLITTER_CHUNK_SIZE = int(os.environ.get('SPLITTER_CHUNK_SIZE', '1000'))
SPLITTER_CHUNK_OVERLAP = int(os.environ.get('SPLITTER_CHUNK_OVERLAP', '100'))

# 调试模式配置
DEBUG_MODE = os.environ.get('DEBUG_MODE', 'True').lower() in ('true', '1', 't')

# 重排序配置
RERANK_ENABLED = os.environ.get('RERANK_ENABLED', 'False').lower() in ('true', '1', 't')
RERANK_TYPE = os.environ.get('RERANK_TYPE', 'cross_encoder')  # similarity, cross_encoder, llm, hybrid
RERANK_TOP_K = int(os.environ.get('RERANK_TOP_K', '3'))  # 重排序后返回的文档数量
RERANK_MODEL_NAME = os.environ.get('RERANK_MODEL_NAME', 'cross-encoder/ms-marco-MiniLM-L-6-v2')
RERANK_MAX_DOCS_FOR_LLM = int(os.environ.get('RERANK_MAX_DOCS_FOR_LLM', '10'))  # LLM重排序的最大文档数